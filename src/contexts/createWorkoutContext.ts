import {useCallback, useEffect, useState} from 'react';
import {
  useCurrentTimeZoneWithDefault,
  useDateEveryMinuteChanged,
  useEffectWhenChangedToFalse,
} from '@hooks';
import type {ImageUrl, Initializer, UUIDString, Workout, WorkoutLink, WorkoutType} from '@types';
import {
  dateToRawTimestamp,
  getWorkoutNameFromType,
  isRawTimestampsEqual,
  timestampToDate,
} from '@types';
import {
  areWorkoutsEqual,
  createWorkoutCopiesForParticipants,
  getAllFulfilledOrThrow,
  getInitializer,
  isEmptyArray,
  isNonEmptyArray,
  isWorkoutTodayOrBefore,
  LOGGER,
} from '@utils';
import {useWorkoutMutation} from './firestore';

const useWorkoutSubmit = (
  isFirstCreate: boolean,
  callbackBefore?: () => void,
  callbackAfter?: () => void,
) => {
  const {isPending: isLoading, mutateAsync} = useWorkoutMutation();
  const submit = useCallback(
    async (workout: Workout) => {
      callbackBefore?.();
      const workoutWithTimestamps = {
        ...workout,
        lastUpdatedDateTime: dateToRawTimestamp(new Date()),
      };
      if (isFirstCreate) {
        const workoutCopies = createWorkoutCopiesForParticipants(workoutWithTimestamps);
        const promises = workoutCopies.map(w => mutateAsync(w));
        await getAllFulfilledOrThrow(promises);
      } else {
        await mutateAsync(workoutWithTimestamps);
      }
      callbackAfter?.();
      LOGGER.debug('[Workout] Workout submitted successfully', workoutWithTimestamps.id);
    },
    [callbackAfter, callbackBefore, isFirstCreate, mutateAsync],
  );
  return {submit, isLoading};
};

// eslint-disable-next-line max-lines-per-function -- TODO refactor
export const useWorkoutState = (
  workoutState: Workout,
  isFirstCreate: boolean,
  onSubmitSuccess?: () => void,
) => {
  const [workout, setWorkout] = useState(workoutState);
  const [isPendingSubmit, setIsPendingSubmit] = useState(false);
  useEffect(() => {
    setWorkout(prevWorkoutState => {
      if (prevWorkoutState !== workoutState) {
        setIsPendingSubmit(false);
        return workoutState;
      }
      return prevWorkoutState;
    });
  }, [workoutState]);

  useEffectWhenChangedToFalse(isPendingSubmit, isFirstCreate ? undefined : onSubmitSuccess);
  const editingStartCallback = useCallback(() => {
    setIsPendingSubmit(true);
  }, []);
  const {isLoading, submit} = useWorkoutSubmit(
    isFirstCreate,
    isFirstCreate ? undefined : editingStartCallback,
    isFirstCreate ? onSubmitSuccess : undefined,
  );

  const onSubmit = useCallback(() => submit(workout), [submit, workout]);

  const onNameChange = useCallback((workoutName: string) => {
    setWorkout(prev => ({
      ...prev,
      workoutName,
    }));
  }, []);

  const onNotesChange = useCallback((notes: string) => {
    setWorkout(prev => ({
      ...prev,
      notes,
    }));
  }, []);

  const onAddParticipant = useCallback((id: UUIDString) => {
    setWorkout(prev => {
      const participantIds = [...prev.participantIds, id];
      return {...prev, participantIds};
    });
  }, []);

  const onRemoveParticipant = useCallback((id: UUIDString) => {
    setWorkout(prev => {
      const participantIds = prev.participantIds.filter(p => p !== id);
      return {...prev, participantIds};
    });
  }, []);

  const onStartDateChange = useCallback((date: Date, type: 'date' | 'time') => {
    setWorkout(prev => {
      const newStartedDate = timestampToDate(prev.startedDateTime);
      const newEndedDate = timestampToDate(prev.endedDateTime);
      if (type === 'time') {
        newStartedDate.setHours(date.getHours(), date.getMinutes(), 0, 0);
        if (newStartedDate > newEndedDate) {
          newEndedDate.setHours(date.getHours(), date.getMinutes(), 0, 0);
        }
      }
      if (type === 'date') {
        // Always set the end AND start date since they are always on the same day
        newStartedDate.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
        newEndedDate.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
      }
      const startedDateTime = dateToRawTimestamp(newStartedDate);
      const endedDateTime = dateToRawTimestamp(newEndedDate);

      const hasStartDateChanged = !isRawTimestampsEqual(prev.startedDateTime, startedDateTime);
      const hasEndDateChanged = !isRawTimestampsEqual(prev.endedDateTime, endedDateTime);

      const isStartInPast = newStartedDate < new Date();
      const {isCompleted: _, ...prevWithoutCompleted} = prev;
      return {
        ...prevWithoutCompleted,
        startedDateTime: hasStartDateChanged ? startedDateTime : prev.startedDateTime,
        endedDateTime: hasEndDateChanged ? endedDateTime : prev.endedDateTime,
        ...(isStartInPast ? {isCompleted: true} : {}),
      };
    });
  }, []);

  const onEndDateChange = useCallback((date: Date, type: 'date' | 'time') => {
    setWorkout(prev => {
      const newStartedDate = timestampToDate(prev.startedDateTime);
      const newEndedDate = timestampToDate(prev.endedDateTime);

      if (type === 'time') {
        // Set the full date and time from the provided date
        newEndedDate.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
        newEndedDate.setHours(date.getHours(), date.getMinutes(), 0, 0);
      }

      if (type === 'date') {
        // Only set the end date, allow different days
        newEndedDate.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
      }

      const startedDateTime = dateToRawTimestamp(newStartedDate);
      const endedDateTime = dateToRawTimestamp(newEndedDate);

      const hasStartDateChanged = !isRawTimestampsEqual(prev.startedDateTime, startedDateTime);
      const hasEndDateChanged = !isRawTimestampsEqual(prev.endedDateTime, endedDateTime);

      const isStartInPast = newStartedDate < new Date();
      const {isCompleted: _, ...prevWithoutCompleted} = prev;
      return {
        ...prevWithoutCompleted,
        startedDateTime: hasStartDateChanged ? startedDateTime : prev.startedDateTime,
        endedDateTime: hasEndDateChanged ? endedDateTime : prev.endedDateTime,
        ...(isStartInPast ? {isCompleted: true} : {}),
      };
    });
  }, []);

  const onTypeChange = useCallback((type: WorkoutType[] | undefined) => {
    setWorkout(prev => {
      if (!type) return {...prev, type: []};
      const isFirstAddition = isEmptyArray(prev.type) && isNonEmptyArray(type);
      return {
        ...prev,
        ...(isFirstAddition ? {workoutName: getWorkoutNameFromType(type.at(0)!)} : {}),
        type,
      };
    });
  }, []);

  const onIsCompletedChange = useCallback(() => {
    setWorkout(prev => {
      const {isCompleted, ...withoutCompleted} = prev;
      return {
        ...withoutCompleted,
        ...(isCompleted ? {} : {isCompleted: true}),
      };
    });
  }, []);

  const onLinksChanged = useCallback((initializer: Initializer<WorkoutLink[] | undefined>) => {
    setWorkout(prev => {
      const newLinks = getInitializer(initializer, prev.links);
      const {links: _, ...prevWithoutLinks} = prev;
      if (!newLinks || isEmptyArray(newLinks)) return prevWithoutLinks;
      return {
        ...prev,
        links: newLinks,
      };
    });
  }, []);

  const onImagesChanged = useCallback((initializer: Initializer<ImageUrl[] | undefined>) => {
    setWorkout(prev => {
      const newImages = getInitializer(initializer, prev.images);
      const {images: _, ...prevWithoutImages} = prev;
      if (!newImages || isEmptyArray(newImages)) return prevWithoutImages;
      return {
        ...prev,
        images: newImages,
      };
    });
  }, []);

  const isChanged = isFirstCreate || !areWorkoutsEqual(workout, workoutState);

  return {
    workout,
    onSubmit,
    isLoading,
    isChanged,
    onNameChange,
    onNotesChange,
    onStartDateChange,
    onEndDateChange,
    onAddParticipant,
    onRemoveParticipant,
    onTypeChange,
    onIsCompletedChange,
    onLinksChanged,
    onImagesChanged,
  };
};

export const useIsWorkoutTodayOrBefore = (workout: Workout) => {
  const timeZone = useCurrentTimeZoneWithDefault();
  const currentDate = useDateEveryMinuteChanged([workout.startedDateTime]);
  return isWorkoutTodayOrBefore(workout, currentDate, timeZone);
};
